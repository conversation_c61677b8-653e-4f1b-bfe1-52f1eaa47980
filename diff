diff --git a/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php b/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
index 5529cd649..202b35f6f 100644
--- a/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
+++ b/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
@@ -18,6 +18,7 @@ interface ShipTableRatesInterface
     const WEIGHT = 'weight';
     const SHIPPING_PRICE = 'shipping_price';
     const FREE_SHIPPING = 'free_shipping';
+    const MIN_ORDER_AMOUNT = 'min_order_amount';
 
     /**
      * Get shiptablerates_id
@@ -175,5 +176,18 @@ interface ShipTableRatesInterface
      */
     public function setFreeShipping(bool $freeShipping): ShipTableRatesInterface;
 
+    /**
+     * Get minimum order amount
+     * @return float|null
+     */
+    public function getMinOrderAmount(): ?float;
+
+    /**
+     * Set minimum order amount
+     * @param float $minOrderAmount
+     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
+     */
+    public function setMinOrderAmount(float $minOrderAmount): ShipTableRatesInterface;
+
 }
 
diff --git a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php
index e3f223dd4..e9e6685ee 100644
--- a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php
+++ b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php
@@ -10,7 +10,7 @@
  */
 namespace Coditron\CustomShippingRate\Controller\ShipTableRates;
 
-class Manage extends \Webkul\MpSellerCategory\Controller\AbstractCategory
+class Manage extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 {
     /**
      * Execute Method
diff --git a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
index a848ecfba..05161991b 100644
--- a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
+++ b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
@@ -51,12 +51,24 @@ class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 
                 $sellerShiprate->save();
                 $id = $sellerShiprate->getShiptableratesId();
-                $this->messageManager->addSuccess(__("Shipping Rate saved successfully."));
-                $this->_helper->clearCache();
-                return $this->resultRedirectFactory->create()->setPath(
-                    'coditron_customshippingrate/shiptablerates/edit',
-                    ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()]
-                );
+
+                // Check if this is a threshold save
+                $isThreshold = $this->getRequest()->getParam('is_threshold', false);
+                if ($isThreshold) {
+                    $this->messageManager->addSuccess(__("Free Shipping Threshold saved successfully."));
+                    $this->_helper->clearCache();
+                    return $this->resultRedirectFactory->create()->setPath(
+                        'coditron_customshippingrate/shiptablerates/editthreshold',
+                        ['id' => $id, '_secure' => $this->getRequest()->isSecure()]
+                    );
+                } else {
+                    $this->messageManager->addSuccess(__("Shipping Rate saved successfully."));
+                    $this->_helper->clearCache();
+                    return $this->resultRedirectFactory->create()->setPath(
+                        'coditron_customshippingrate/shiptablerates/edit',
+                        ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()]
+                    );
+                }
             } catch (\Exception $e) {
                 $this->messageManager->addError($e->getMessage());
                 return $this->resultRedirectFactory->create()->setPath(
diff --git a/app/code/Coditron/CustomShippingRate/Model/Carrier.php b/app/code/Coditron/CustomShippingRate/Model/Carrier.php
index 8d1ad90e1..344c44b63 100644
--- a/app/code/Coditron/CustomShippingRate/Model/Carrier.php
+++ b/app/code/Coditron/CustomShippingRate/Model/Carrier.php
@@ -158,6 +158,11 @@ class Carrier extends AbstractCarrier implements CarrierInterface
                     continue;
                 }
 
+                // Skip free shipping thresholds from regular rates
+                if ($tableRate->getMinAmount() && $tableRate->getMinAmount() > 0) {
+                    continue;
+                }
+
                 [, $sellerId] = explode('|', $sellerKey);
                 $totalPrice += $tableRate->getShippingPrice();
                 $minLeadTime = $minLeadTime === null ? $tableRate->getTotalLeadTime() : min($minLeadTime, $tableRate->getTotalLeadTime());
diff --git a/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php b/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
index 5369590c3..7c24e25d2 100644
--- a/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
+++ b/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
@@ -215,6 +215,22 @@ class ShipTableRates extends AbstractModel implements ShipTableRatesInterface
         return $this->setData(self::FREE_SHIPPING, $freeShipping);
     }
 
+    /**
+     * @inheritDoc
+     */
+    public function getMinOrderAmount(): ?float
+    {
+        return (float)$this->getData(self::MIN_ORDER_AMOUNT);
+    }
+
+    /**
+     * @inheritDoc
+     */
+    public function setMinOrderAmount(float $minOrderAmount): ShipTableRatesInterface
+    {
+        return $this->setData(self::MIN_ORDER_AMOUNT, $minOrderAmount);
+    }
+
     /**
      * Override to Converts countries array to string
      * @param $key
diff --git a/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php b/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
index 4043e1b79..0c3b4c2e7 100755
--- a/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
+++ b/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
@@ -15,6 +15,7 @@ use Magento\Quote\Model\Quote\Address\Total;
 use Magento\Quote\Model\Quote\Address\Total\Shipping;
 use Coditron\CustomShippingRate\Helper\Data;
 use Coditron\CustomShippingRate\Model\Carrier;
+use Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface;
 
 class ShippingPlugin
 {
@@ -23,13 +24,21 @@ class ShippingPlugin
      */
     protected $customShippingRateHelper;
 
+    /**
+     * @var ShipTableRatesRepositoryInterface
+     */
+    protected $shipTableRatesRepository;
+
     /**
      * @param Data $customShippingRateHelper
+     * @param ShipTableRatesRepositoryInterface $shipTableRatesRepository
      */
     public function __construct(
-        Data $customShippingRateHelper
+        Data $customShippingRateHelper,
+        ShipTableRatesRepositoryInterface $shipTableRatesRepository
     ) {
         $this->customShippingRateHelper = $customShippingRateHelper;
+        $this->shipTableRatesRepository = $shipTableRatesRepository;
     }
 
     /**
@@ -54,7 +63,7 @@ class ShippingPlugin
 
         if (!$this->customShippingRateHelper->isEnabled($storeId)
             || $address->getAddressType() != Address::ADDRESS_TYPE_SHIPPING
-            || strpos((string) $method, Carrier::CODE) === false
+            || (strpos((string) $method, Carrier::CODE) === false && strpos((string) $method, 'freeshipping') === false)
         ) {
             return $proceed($quote, $shippingAssignment, $total);
         }
@@ -65,7 +74,15 @@ class ShippingPlugin
             //update shipping code
             $shipping->setMethod($customShippingOption['code']);
             $address->setShippingMethod($customShippingOption['code']);
+
             $this->updateCustomRate($address, $customShippingOption);
+
+            // For free shipping methods, ensure totals are set to zero
+            if (strpos($method, 'freeshipping') === 0) {
+                $total->setShippingAmount(0);
+                $total->setBaseShippingAmount(0);
+                $total->setShippingDescription($customShippingOption['description'] ?? 'Free Shipping');
+            }
         }
 
         return $proceed($quote, $shippingAssignment, $total);
@@ -108,11 +125,21 @@ class ShippingPlugin
                 $rate = $selectedRate->getPrice();
             }
 
-            $jsonToArray = [
-                'code' => $json,
-                'type' => $this->customShippingRateHelper->getShippingCodeFromMethod($json, $storeId),
-                'rate' => $rate
-            ];
+            // Handle free shipping codes
+            if (strpos($json, 'freeshipping') === 0) {
+                $jsonToArray = [
+                    'code' => $json,
+                    'type' => 'free_shipping',
+                    'rate' => 0, // Free shipping
+                    'description' => 'Free Shipping'
+                ];
+            } else {
+                $jsonToArray = [
+                    'code' => $json,
+                    'type' => $this->customShippingRateHelper->getShippingCodeFromMethod($json, $storeId),
+                    'rate' => $rate
+                ];
+            }
 
             return $this->formatShippingArray($jsonToArray);
         }
@@ -147,6 +174,8 @@ class ShippingPlugin
         return $selectedRate;
     }
 
+
+
     /**
      * @param $jsonToArray array
      * @return array
diff --git a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
index 12f42277c..b76c8f100 100644
--- a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
+++ b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
@@ -55,6 +55,9 @@ class ShippingRateListDataProvider extends \Magento\Ui\DataProvider\AbstractData
         $this->collection->addFieldToFilter(
             'seller_id',
             ['eq' => $this->helper->getSellerId()]
+        )->addFieldToFilter(
+            'min_order_amount',
+            ['eq' => 0]
         );
     }
 }
diff --git a/app/code/Coditron/CustomShippingRate/etc/db_schema.xml b/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
index 68559032e..f1710aec1 100644
--- a/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
+++ b/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
@@ -21,6 +21,7 @@
         <column name="weight" nullable="true" xsi:type="varchar" comment="Weight (Up to, in Kg)" length="255"/>
         <column name="shipping_price" nullable="true" xsi:type="varchar" comment="Shipping price" length="255"/>
         <column name="free_shipping" nullable="false" xsi:type="boolean" default="false" comment="Free Shipping"/>
+        <column name="min_order_amount" nullable="true" xsi:type="decimal" precision="12" scale="4" default="0.0000" comment="Minimum Order Amount for Free Shipping"/>
 		<column name="seller_id" xsi:type="int" unsigned="true" nullable="false" identity="false" padding="10" default="0" comment="Seller Id"/>
 		<constraint xsi:type="foreign" referenceId="FK_CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" table="coditron_customshippingrate_shiptablerates" column="seller_id" referenceTable="customer_entity" referenceColumn="entity_id" onDelete="CASCADE"/>
         <index referenceId="CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" indexType="btree">
diff --git a/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json b/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
index 3d5012378..c9b75af9f 100644
--- a/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
+++ b/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
@@ -22,6 +22,7 @@
             "weight": true,
             "shipping_price": true,
             "free_shipping": true,
+            "min_order_amount": true,
             "seller_id": true
         },
         "index": {
diff --git a/app/code/Coditron/CustomShippingRate/etc/di.xml b/app/code/Coditron/CustomShippingRate/etc/di.xml
index 68c6a903c..96187ef7f 100644
--- a/app/code/Coditron/CustomShippingRate/etc/di.xml
+++ b/app/code/Coditron/CustomShippingRate/etc/di.xml
@@ -6,6 +6,9 @@
 	<type name="Magento\Checkout\Model\ShippingInformationManagement">
 		<plugin name="update_shipping_data_on_method_selection" type="Coditron\CustomShippingRate\Plugin\UpdateShippingDataOnMethodSelection" />
 	</type>
+	<type name="Magento\OfflineShipping\Model\Carrier\Freeshipping">
+		<plugin name="disable_core_freeshipping_when_thresholds_exist" type="Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier\FreeShippingPlugin" />
+	</type>
 	<preference for="Coditron\CustomShippingRate\Api\CustomShippingInformationManagementInterface" type="Coditron\CustomShippingRate\Model\CustomShippingInformationManagement" />
 	<preference for="Coditron\CustomShippingRate\Api\Data\CustomShippingInformationInterface"
                 type="Coditron\CustomShippingRate\Model\Data\CustomShippingInformation" />
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml b/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
index 972c401ce..73e7e885b 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
@@ -20,6 +20,7 @@
         </referenceContainer>
         <referenceContainer name="sellership_rate_manage">
             <uiComponent name="sellership_rates_list_front"/>
+            <uiComponent name="sellership_threshold_list_front"/>
         </referenceContainer>
     </body>
 </page>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
index 3894ab393..d90854804 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
@@ -1,7 +1,7 @@
 <?php
      $marketplaceHelper = $block->getMpHelper();
      $isPartner= $marketplaceHelper->isSeller();
-     $isEnable = false;
+     $isEnable = true;
      $magentoCurrentUrl = $block->getCurrentUrl();
 ?>
 <?php if ($isPartner): ?>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
index 15601be5f..dc6e465d1 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
@@ -10,21 +10,51 @@
  */
 ?>
 <div class="wk-mpsellercategory-container">
-    <div class="page-main-actions">
-        <div class="page-actions-placeholder"></div>
-        <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
-            <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
-                <div class="page-actions-buttons">
-                    <button id="add" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
-                    class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
-                    onclick="location.href
-                    = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
-                    data-ui-id="add-button">
-                        <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
-                    </button>
+    <!-- Shipping Methods Section -->
+    <div class="wk-mp-section">
+        <h3 class="wk-mp-section-title"><?= $escaper->escapeHtml(__('Manage Shipping Methods')) ?></h3>
+        <div class="page-main-actions">
+            <div class="page-actions-placeholder"></div>
+            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
+                    <div class="page-actions-buttons">
+                        <button id="add-shipping-method" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
+                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
+                        onclick="location.href
+                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
+                        data-ui-id="add-button">
+                            <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
+                        </button>
+                    </div>
                 </div>
             </div>
         </div>
+        <div id="shipping-methods-grid">
+            <?= /* @noEscape */ $block->getChildHtml('sellership_rates_list_front'); ?>
+        </div>
+    </div>
+
+    <!-- Free Shipping Thresholds Section -->
+    <div class="wk-mp-section" style="margin-top: 40px;">
+        <h3 class="wk-mp-section-title"><?= $escaper->escapeHtml(__('Free Shipping Thresholds')) ?></h3>
+        <div class="page-main-actions">
+            <div class="page-actions-placeholder"></div>
+            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Free Shipping Threshold")); ?>'>
+                    <div class="page-actions-buttons">
+                        <button id="add-threshold" title='<?= $escaper->escapeHtml(__("Add New Threshold")); ?>' type="button"
+                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
+                        onclick="location.href
+                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/newthreshold'))?>';"
+                        data-ui-id="add-threshold-button">
+                            <span><?= $escaper->escapeHtml(__("Add New Threshold")); ?></span>
+                        </button>
+                    </div>
+                </div>
+            </div>
+        </div>
+        <div id="threshold-grid">
+            <?= /* @noEscape */ $block->getChildHtml('sellership_threshold_list_front'); ?>
+        </div>
     </div>
-    <?= /* @noEscape */ $block->getChildHtml(); ?>
 </div>
